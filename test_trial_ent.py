"""
Тестовый скрипт для проверки работы пробного ЕНТ
"""
import asyncio
import logging
from database import init_database, UserRepository, StudentRepository, SubjectRepository, QuestionRepository
from common.trial_ent_service import TrialEntService

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_trial_ent_service():
    """Тестирование сервиса пробного ЕНТ"""
    
    # Инициализируем базу данных
    await init_database()
    
    # Проверяем наличие предметов
    subjects = await SubjectRepository.get_all()
    logger.info(f"Найдено предметов в базе: {len(subjects)}")
    
    for subject in subjects:
        logger.info(f"Предмет: {subject.name} (ID: {subject.id})")
        
        # Проверяем количество вопросов по предмету
        questions = await QuestionRepository.get_by_subject(subject.id)
        logger.info(f"  Вопросов: {len(questions)}")
    
    # Тестируем получение ID предмета по коду
    subject_codes = ["kz", "mathlit", "math", "geo", "bio", "chem", "inf", "world"]
    
    for code in subject_codes:
        subject_id = await TrialEntService.get_subject_id_by_code(code)
        if subject_id:
            logger.info(f"Код {code} -> ID {subject_id}")
        else:
            logger.warning(f"Предмет с кодом {code} не найден")
    
    # Тестируем генерацию вопросов
    logger.info("\nТестирование генерации вопросов...")
    
    required_subjects = ["kz", "mathlit"]
    profile_subjects = ["math", "bio"]
    
    try:
        questions, total_count = await TrialEntService.generate_trial_ent_questions(
            required_subjects, profile_subjects
        )
        
        logger.info(f"Сгенерировано вопросов: {len(questions)} из {total_count}")
        
        # Группируем по предметам
        subject_counts = {}
        for question in questions:
            subject_code = question["subject_code"]
            subject_counts[subject_code] = subject_counts.get(subject_code, 0) + 1
        
        for subject_code, count in subject_counts.items():
            subject_name = TrialEntService.get_subject_name(subject_code)
            logger.info(f"  {subject_name} ({subject_code}): {count} вопросов")
        
        # Показываем пример вопроса
        if questions:
            example_question = questions[0]
            logger.info(f"\nПример вопроса:")
            logger.info(f"  Номер: {example_question['number']}")
            logger.info(f"  Предмет: {example_question['subject_name']}")
            logger.info(f"  Текст: {example_question['text']}")
            logger.info(f"  Варианты ответов: {len(example_question['options'])}")
            
    except Exception as e:
        logger.error(f"Ошибка при генерации вопросов: {e}")
    
    # Проверяем наличие студентов
    logger.info("\nПроверка студентов...")
    users = await UserRepository.get_all()
    students = await StudentRepository.get_all()
    
    logger.info(f"Пользователей в системе: {len(users)}")
    logger.info(f"Студентов в системе: {len(students)}")
    
    if students:
        student = students[0]
        logger.info(f"Пример студента: {student.user.name} (ID: {student.id})")


async def create_test_data():
    """Создание тестовых данных для проверки"""
    
    # Инициализируем базу данных
    await init_database()
    
    # Проверяем, есть ли уже предметы
    subjects = await SubjectRepository.get_all()
    
    if not subjects:
        logger.info("Создание тестовых предметов...")
        
        # Создаем предметы
        subject_names = [
            "История Казахстана",
            "Математическая грамотность", 
            "Математика",
            "География",
            "Биология",
            "Химия",
            "Информатика",
            "Всемирная история"
        ]
        
        for name in subject_names:
            try:
                subject = await SubjectRepository.create(name)
                logger.info(f"Создан предмет: {subject.name}")
            except Exception as e:
                logger.error(f"Ошибка при создании предмета {name}: {e}")
    
    logger.info("Тестовые данные готовы")


async def main():
    """Главная функция"""
    logger.info("Запуск тестирования пробного ЕНТ...")
    
    # Создаем тестовые данные если нужно
    await create_test_data()
    
    # Тестируем сервис
    await test_trial_ent_service()
    
    logger.info("Тестирование завершено")


if __name__ == "__main__":
    asyncio.run(main())
