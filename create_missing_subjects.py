"""
Скрипт для создания недостающих предметов для пробного ЕНТ
"""
import asyncio
import logging
from database import init_database, SubjectRepository

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def create_missing_subjects():
    """Создание недостающих предметов"""
    
    # Инициализируем базу данных
    await init_database()
    
    # Список недостающих предметов
    missing_subjects = [
        "Математическая грамотность",
        "География", 
        "Информатика",
        "Всемирная история"
    ]
    
    logger.info("Создание недостающих предметов...")
    
    for subject_name in missing_subjects:
        try:
            # Проверяем, существует ли уже такой предмет
            existing = await SubjectRepository.get_by_name(subject_name)
            if existing:
                logger.info(f"Предмет '{subject_name}' уже существует (ID: {existing.id})")
                continue
            
            # Создаем предмет
            subject = await SubjectRepository.create(subject_name)
            logger.info(f"Создан предмет: {subject.name} (ID: {subject.id})")
            
        except Exception as e:
            logger.error(f"Ошибка при создании предмета '{subject_name}': {e}")
    
    logger.info("Создание предметов завершено")
    
    # Проверяем все предметы
    logger.info("\nВсе предметы в системе:")
    subjects = await SubjectRepository.get_all()
    for subject in subjects:
        logger.info(f"  {subject.name} (ID: {subject.id})")


if __name__ == "__main__":
    asyncio.run(create_missing_subjects())
