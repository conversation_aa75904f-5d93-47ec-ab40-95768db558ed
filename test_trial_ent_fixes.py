"""
Тестирование исправлений пробного ЕНТ
"""
import asyncio
import logging
from database import init_database, QuestionRepository, AnswerOptionRepository, SubjectRepository

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(name)-20s | %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

async def test_questions_integrity():
    """Проверить целостность вопросов"""
    print("🔍 Проверяем целостность вопросов...")
    
    await init_database()
    
    # Предметы для пробного ЕНТ
    trial_ent_subjects = [
        'История Казахстана', 'Математическая грамотность', 
        'Математика', 'География', 'Биология', 'Химия', 
        'Информатика', 'Всемирная история'
    ]
    
    subjects = await SubjectRepository.get_all()
    subject_map = {s.name: s for s in subjects}
    
    total_problems = 0
    
    for subject_name in trial_ent_subjects:
        if subject_name in subject_map:
            subject = subject_map[subject_name]
            questions = await QuestionRepository.get_by_subject(subject.id)
            
            problems_count = 0
            for question in questions:
                options = await AnswerOptionRepository.get_by_question(question.id)
                
                if not options:
                    problems_count += 1
                    print(f"  ❌ Вопрос {question.id} ({subject_name}): нет вариантов ответов")
                    continue
                    
                correct_options = [opt for opt in options if opt.is_correct]
                if not correct_options:
                    problems_count += 1
                    print(f"  ❌ Вопрос {question.id} ({subject_name}): нет правильного ответа")
                    continue
            
            if problems_count == 0:
                print(f"  ✅ {subject_name}: {len(questions)} вопросов - все в порядке")
            else:
                print(f"  ⚠️ {subject_name}: {problems_count} проблемных вопросов из {len(questions)}")
                total_problems += problems_count
    
    if total_problems == 0:
        print("\n🎉 Все вопросы в порядке! Проблем не найдено.")
    else:
        print(f"\n⚠️ Найдено {total_problems} проблемных вопросов")
    
    return total_problems == 0


async def test_trial_ent_generation():
    """Тестировать генерацию вопросов пробного ЕНТ"""
    print("\n🧪 Тестируем генерацию вопросов пробного ЕНТ...")
    
    from common.trial_ent_service import TrialEntService
    
    # Тестируем разные комбинации
    test_cases = [
        (['kz'], ['math']),
        (['mathlit'], ['bio']),
        (['kz', 'mathlit'], ['chem', 'geo'])
    ]
    
    all_passed = True
    
    for i, (required, profile) in enumerate(test_cases, 1):
        try:
            print(f"  Тест {i}: {required} + {profile}")
            questions, total = await TrialEntService.generate_trial_ent_questions(required, profile)
            
            if len(questions) == total and total > 0:
                print(f"    ✅ Сгенерировано {total} вопросов")
            else:
                print(f"    ❌ Ошибка: ожидалось {total}, получено {len(questions)}")
                all_passed = False
                
        except Exception as e:
            print(f"    ❌ Ошибка: {e}")
            all_passed = False
    
    return all_passed


async def test_time_diversity():
    """Проверить разнообразие времени ответа"""
    print("\n⏰ Проверяем разнообразие времени ответа...")
    
    subjects = await SubjectRepository.get_all()
    time_counts = {}
    
    for subject in subjects[:3]:  # Проверяем первые 3 предмета
        questions = await QuestionRepository.get_by_subject(subject.id)
        
        for question in questions[:10]:  # Первые 10 вопросов
            time_limit = question.time_limit
            time_counts[time_limit] = time_counts.get(time_limit, 0) + 1
    
    print(f"  Распределение времени: {time_counts}")
    
    # Должно быть минимум 3 разных значения времени
    unique_times = len(time_counts)
    if unique_times >= 3:
        print(f"  ✅ Найдено {unique_times} разных значений времени")
        return True
    else:
        print(f"  ❌ Найдено только {unique_times} значений времени")
        return False


async def main():
    """Основная функция тестирования"""
    print("🚀 Запускаем тестирование исправлений пробного ЕНТ...\n")
    
    # Тест 1: Целостность вопросов
    test1_passed = await test_questions_integrity()
    
    # Тест 2: Генерация вопросов
    test2_passed = await test_trial_ent_generation()
    
    # Тест 3: Разнообразие времени
    test3_passed = await test_time_diversity()
    
    # Итоги
    print("\n" + "="*50)
    print("📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print(f"  {'✅' if test1_passed else '❌'} Целостность вопросов")
    print(f"  {'✅' if test2_passed else '❌'} Генерация вопросов")
    print(f"  {'✅' if test3_passed else '❌'} Разнообразие времени")
    
    all_passed = test1_passed and test2_passed and test3_passed
    
    if all_passed:
        print("\n🎉 ВСЕ ТЕСТЫ ПРОШЛИ УСПЕШНО!")
        print("Система готова к использованию.")
    else:
        print("\n⚠️ НЕКОТОРЫЕ ТЕСТЫ НЕ ПРОШЛИ")
        print("Требуется дополнительная проверка.")
    
    return all_passed


if __name__ == "__main__":
    asyncio.run(main())
